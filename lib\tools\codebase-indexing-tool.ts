/**
 * Codebase Indexing Tool with Adaptive Context Analysis
 *
 * This tool intelligently analyzes codebases by using adaptive truncation strategies
 * to preserve important context while maintaining efficiency. Key features:
 *
 * - Adaptive context limits based on file type (1200-2500 characters)
 * - Smart truncation at natural breakpoints (imports, comments, statements)
 * - Fallback to path-based analysis for robust classification
 * - Cost-optimized LLM usage with Gemini 2.5 Flash
 *
 * Context Limits by File Type:
 * - Config files (JSON, YAML): 2500 chars
 * - Type definitions: 2000 chars
 * - API routes/handlers: 1800 chars
 * - Entry points (index, main): 2000 chars
 * - Database models: 1800 chars
 * - React components: 1600 chars
 * - Test files: 1500 chars
 * - Regular source files: 1200 chars
 */

import { StorageTool } from './storage-tool';
import { vectorEmbeddingTool } from './vector-embeddings';
import * as fs from 'fs/promises';
import { Dirent } from 'fs';
import path from 'path';
import { RecursiveCharacterTextSplitter } from 'langchain/text_splitter';
import { v4 as uuidv4 } from 'uuid';
import { processWithGoogleAI } from './google-ai';
import { processWithGroq } from './groq-ai';
import { codebaseIndexingReportGenerator } from './codebase-indexing-report-generator';
import { CodebaseFileAnalysis } from '../interfaces/CodebaseIndexingReport';

export interface CodebaseIndexingOptions {
  rootPath: string;
  userId: string;
  projectName: string;
  selectedPaths?: string[]; // Specific paths selected by user for targeted analysis
  excludePatterns?: string[];
  includeExtensions?: string[];
  chunkSize?: number;
  chunkOverlap?: number;
  verbose?: boolean; // Added for optional detailed logging
  maxFiles?: number; // Maximum number of files to process (for legacy method)
}

export interface CodebaseIndexingResult {
  success: boolean;
  totalFiles: number;
  totalChunks: number;
  documentId: string;
  chunkIds?: string[];
  error?: string;
}

// Define enriched metadata interface for code chunks
export interface EnrichedCodeChunk {
  content: string;
  metadata: {
    title: string;       // fileName
    filePath: string;
    language: string;
    chunkIndex: number;
    projectName: string;
    type: 'code_chunk';
    indexedAt: string;

    // --- STANDARDIZED CHUNK IDENTIFICATION ---
    doc_id: string;      // Document ID for the codebase session
    chunk_id: string;    // Standardized format: ${docId}_${index + 1}

    // --- ENRICHED METADATA ---
    codeSummary: string;
    imports: string[];
    exports: string[];
    codeEntityType: 'Component' | 'Function' | 'Class' | 'Hook' | 'Configuration' | 'Util' | 'Unknown';
    definedEntities: string[];
    apiEndpoints: string[];
    fileId: string; // UUID for the file (used as namespace)
  }
}

export class CodebaseIndexingTool {
  private storageTool: StorageTool;
  private applicationContext: string | null = null; // Cache for application context
  private costTrackingData: { originalSize: number, truncatedSize: number }[] = []; // Track adaptive truncation savings
  private defaultExcludePatterns = [
    'node_modules',
    '.git',
    '.next',
    'dist',
    'build',
    '.vscode',
    'coverage',
    '.nuxt',
    '.output',
    '__pycache__',
    '.env',
    '.env.local',
    '.env.production',
    '.env.development',
    'package-lock.json',
    'yarn.lock',
    'pnpm-lock.yaml',
    '.DS_Store',
    'Thumbs.db',
    '.cache',
    'tmp',
    'temp'
  ];

  private defaultIncludeExtensions = [
    '.ts', '.tsx', '.js', '.jsx',
    '.py', '.java', '.cpp', '.c',
    '.cs', '.go', '.rs', '.php',
    '.rb', '.swift', '.kt', '.scala',
    '.md', '.txt', '.json', '.yaml', '.yml'
  ];

  constructor() {
    this.storageTool = new StorageTool();
  }

  /**
   * Index codebase directly to vector embeddings with enriched metadata
   * This is the new enhanced version for Path A: Codebase Onboarding & Enrichment
   */
  async indexCodebaseDirect(options: CodebaseIndexingOptions): Promise<CodebaseIndexingResult> {
    const processingStartTime = Date.now();
    const verbose = options.verbose ?? false; // Use verbose flag

    try {
      // Reset cost tracking for this indexing operation
      this.costTrackingData = [];

      console.log(`🚀 Starting enriched codebase indexing for ${options.projectName}`);

      // **PHASE 1: Application Context Analysis**
      console.log(`🧠 Analyzing application context...`);
      await this.analyzeApplicationContext(options.rootPath);

      const excludePatterns = options.excludePatterns || this.defaultExcludePatterns;
      const includeExtensions = options.includeExtensions || this.defaultIncludeExtensions;

      // Find all relevant files using the optimized function
      const files = await this.findCodeFiles(
        options.rootPath,
        excludePatterns,
        includeExtensions,
        verbose, // Pass the verbose flag
        options.selectedPaths // Pass the selectedPaths for targeted scanning
      );

      console.log(`📁 Found ${files.length} code files to index for enrichment`);

      if (files.length === 0) {
        return {
          success: false,
          totalFiles: 0,
          totalChunks: 0,
          documentId: '',
          error: 'No files found to index'
        };
      }

      // Initialize vector embedding tool
      if (!vectorEmbeddingTool.isInitialized()) {
        await vectorEmbeddingTool.initialize();
      }

      // Step 1: Process all files to generate enriched chunks in memory
      const allChunks: EnrichedCodeChunk[] = [];
      const fileAnalysisData: CodebaseFileAnalysis[] = [];

      for (let i = 0; i < files.length; i++) {
        const filePath = files[i];
        const fileProcessingStart = Date.now();
        const relativePath = path.relative(options.rootPath, filePath);

        try {
          // Generate file-level UUID for namespace
          const fileId = uuidv4();

          // Enhanced progress reporting
          const progressPercent = ((i + 1) / files.length * 100).toFixed(1);
          console.log(`🧠 Processing file ${i + 1}/${files.length} (${progressPercent}%): ${relativePath}`);

          // Get file stats
          const fileStats = await fs.stat(filePath);
          const fileName = path.basename(filePath);

          const enrichedChunks = await this.processAndEnrichFile(filePath, options, fileId);
          allChunks.push(...enrichedChunks);

          // Collect file analysis data for reporting
          if (enrichedChunks.length > 0) {
            const firstChunk = enrichedChunks[0];
            const fileAnalysis: CodebaseFileAnalysis = {
              filePath: relativePath,
              fileName,
              language: firstChunk.metadata.language,
              fileSize: fileStats.size,
              chunkCount: enrichedChunks.length,
              llmSummary: firstChunk.metadata.codeSummary,
              codeEntityType: firstChunk.metadata.codeEntityType,
              definedEntities: firstChunk.metadata.definedEntities,
              imports: firstChunk.metadata.imports,
              exports: firstChunk.metadata.exports,
              apiEndpoints: firstChunk.metadata.apiEndpoints,
              processingTimeMs: Date.now() - fileProcessingStart,
              success: true,
              chunks: enrichedChunks.map((chunk, index) => ({
                chunkId: `${fileId}_${index + 1}`,
                chunkIndex: index,
                contentPreview: chunk.content.substring(0, 200),
                summary: chunk.metadata.codeSummary
              }))
            };
            fileAnalysisData.push(fileAnalysis);
          }
        } catch (error) {
          const errorMessage = error instanceof Error ? error.message : 'Unknown error';
          console.warn(`⚠️ Failed to process file ${relativePath}: ${errorMessage}`);

          const fileName = path.basename(filePath);

          // Try to get file size even if processing failed
          let fileSize = 0;
          try {
            const stats = await fs.stat(filePath);
            fileSize = stats.size;
          } catch (statError) {
            // File might not exist or be accessible
          }

          const fileAnalysis: CodebaseFileAnalysis = {
            filePath: relativePath,
            fileName,
            language: 'Unknown',
            fileSize,
            chunkCount: 0,
            llmSummary: 'Failed to process',
            codeEntityType: 'Unknown',
            definedEntities: [],
            imports: [],
            exports: [],
            apiEndpoints: [],
            processingTimeMs: Date.now() - fileProcessingStart,
            success: false,
            errorMessage,
            chunks: []
          };
          fileAnalysisData.push(fileAnalysis);
        }
      }

      console.log(`🧠 Generated a total of ${allChunks.length} enriched chunks. Starting vector embedding.`);

      const chunkIds: string[] = [];
      const batchSize = 20; 
      const docId = uuidv4();
      let globalChunkIndex = 0;

      for (let i = 0; i < allChunks.length; i += batchSize) {
        const batch = allChunks.slice(i, i + batchSize);
        console.log(`📦 Uploading batch ${Math.floor(i/batchSize) + 1}/${Math.ceil(allChunks.length/batchSize)} to vector store.`);

        const upsertPromises = batch.map(async (chunk, batchIndex) => {
          const chunkId = `${docId}_${globalChunkIndex + batchIndex + 1}`;
          const updatedMetadata = { ...chunk.metadata, doc_id: docId, chunk_id: chunkId };
          const sanitizedMetadata = this.sanitizeMetadataForPinecone(updatedMetadata);
          await vectorEmbeddingTool.createEmbedding(
            chunk.content,
            chunkId,
            sanitizedMetadata,
            chunk.metadata.fileId 
          );
          return chunkId;
        });

        const createdIds = await Promise.all(upsertPromises);
        chunkIds.push(...createdIds);
        globalChunkIndex += batch.length;
      }

      // Log cost optimization statistics
      if (this.costTrackingData.length > 0) {
        this.logCostOptimization(this.costTrackingData);
        this.costTrackingData = []; // Reset for next indexing operation
      }

      console.log(`✅ Successfully indexed and enriched codebase directly!`);
      console.log(`📊 Total files: ${files.length}`);
      console.log(`📄 Total chunks: ${allChunks.length}`);

      try {
        console.log(`📋 Generating codebase indexing completion report...`);
        const indexingResult: CodebaseIndexingResult = {
          success: true,
          totalFiles: files.length,
          totalChunks: allChunks.length,
          documentId: docId,
          chunkIds: chunkIds
        };
        const report = await codebaseIndexingReportGenerator.generateReport(
          indexingResult,
          options.projectName,
          options.userId,
          fileAnalysisData,
          processingStartTime
        );
        const reportPdfUrl = await codebaseIndexingReportGenerator.saveReport(report);
        console.log(`📄 Indexing completion report saved: ${reportPdfUrl}`);
        return indexingResult;
      } catch (reportError) {
        console.warn(`⚠️ Failed to generate completion report:`, reportError);
        return {
          success: true,
          totalFiles: files.length,
          totalChunks: allChunks.length,
          documentId: docId,
          chunkIds: chunkIds
        };
      }

    } catch (error) {
      console.error('❌ Error in direct codebase indexing:', error);
      return {
        success: false,
        totalFiles: 0,
        totalChunks: 0,
        documentId: '',
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  }

  /**
   * **PHASE 1: Application Context Analysis**
   */
  private async analyzeApplicationContext(rootPath: string): Promise<void> {
    if (this.applicationContext) {
      console.log(`✅ Using cached application context`);
      return;
    }

    try {
      const entryPoints = [
        path.join(rootPath, 'app', 'page.tsx'), path.join(rootPath, 'app', 'page.jsx'),
        path.join(rootPath, 'src', 'app', 'page.tsx'), path.join(rootPath, 'src', 'app', 'page.jsx'),
        path.join(rootPath, 'pages', 'index.tsx'), path.join(rootPath, 'pages', 'index.jsx'),
        path.join(rootPath, 'src', 'pages', 'index.tsx'), path.join(rootPath, 'src', 'pages', 'index.jsx'),
        path.join(rootPath, 'src', 'App.tsx'), path.join(rootPath, 'src', 'App.jsx'),
        path.join(rootPath, 'App.tsx'), path.join(rootPath, 'App.jsx')
      ];

      let entryPointContent = '';
      let entryPointPath = '';

      for (const entryPoint of entryPoints) {
        try {
          entryPointContent = await fs.readFile(entryPoint, 'utf-8');
          entryPointPath = path.relative(rootPath, entryPoint);
          console.log(`📍 Found application entry point: ${entryPointPath}`);
          break;
        } catch (error) { /* continue */ }
      }

      if (!entryPointContent) {
        console.log(`⚠️  No main entry point found, using generic application context`);
        this.applicationContext = "Generic web application - specific context unavailable";
        return;
      }

      const prompt = `
You are analyzing the main entry point of a software application to understand its overall purpose and architecture.
**File**: ${entryPointPath}
**Task**: Provide a comprehensive analysis of this application's purpose, architecture, and domain.
**Analysis Requirements:**
1. **Application Purpose**: What is the main goal/function of this application?
2. **Architecture Pattern**: What framework/architecture is being used? (Next.js, React, etc.)
3. **Domain/Industry**: What business domain does this application serve?
4. **Key Features**: What are the main features/capabilities based on imports and components?
5. **User Interface**: What type of UI/UX patterns are evident?
6. **Data Flow**: How does data appear to flow through the application?
**Instructions:**
- Focus on imports, component structure, routing, and any comments
- Provide a 3-4 sentence summary that captures the essence of the application
Respond with ONLY a JSON object:
{
    "applicationPurpose": "Brief description of what this application does",
    "architecture": "Framework and architectural patterns used",
    "domain": "Business domain/industry this serves",
    "keyFeatures": ["feature1", "feature2", "feature3"],
    "summary": "3-4 sentence comprehensive summary of the application's purpose and architecture"
}
**APPLICATION ENTRY POINT CODE:**
\`\`\`typescript
${entryPointContent}
\`\`\`
      `;

      console.log(`🧠 Analyzing application context with Google Gemini 2.5 Pro...`);
      const result = await processWithGoogleAI({ prompt, model: "gemini-2.5-pro" });
      const jsonString = result.substring(result.indexOf('{'), result.lastIndexOf('}') + 1);
      const analysis = JSON.parse(jsonString);

      this.applicationContext = `
Application Purpose: ${analysis.applicationPurpose || 'Unknown'}
Architecture: ${analysis.architecture || 'Unknown'}
Domain: ${analysis.domain || 'Unknown'}
Key Features: ${Array.isArray(analysis.keyFeatures) ? analysis.keyFeatures.join(', ') : 'Unknown'}
Summary: ${analysis.summary || 'No summary available'}
      `.trim();

      console.log(`✅ Application context analysis complete`);
      console.log(`📋 Context: ${this.applicationContext.split('\n')[0]}...`);
    } catch (error) {
      console.warn(`⚠️  Application context analysis failed:`, error);
      this.applicationContext = "Application context analysis failed - using generic context";
    }
  }

  async indexCodebase(options: CodebaseIndexingOptions): Promise<CodebaseIndexingResult> {
    return this.indexCodebaseDirect(options);
  }

  async indexCodebaseLegacy(options: CodebaseIndexingOptions): Promise<CodebaseIndexingResult> {
    const verbose = options.verbose ?? false;
    try {
      console.log(`Starting codebase indexing for ${options.projectName}`);
      const excludePatterns = options.excludePatterns || this.defaultExcludePatterns;
      const includeExtensions = options.includeExtensions || this.defaultIncludeExtensions;
      
      const files = await this.findCodeFiles(
        options.rootPath,
        excludePatterns,
        includeExtensions,
        verbose
        // No selectedPaths - this method scans the entire rootPath
      );
      
      console.log(`Found ${files.length} code files to index`);

      // Allow configuration of max files, but default to processing all files
      const maxFiles = options.maxFiles || files.length;
      const filesToProcess = files.length > maxFiles ? files.slice(0, maxFiles) : files;
      if (files.length > maxFiles) {
        console.log(`⚠️ Large codebase detected (${files.length} files). Processing first ${maxFiles} files to prevent timeout.`);
        console.log(`💡 To process all files, increase the maxFiles option or use the direct indexing method.`);
      }

      const batchSize = 10;
      let totalChunks = 0;
      const allContent: string[] = [];
      for (let i = 0; i < filesToProcess.length; i += batchSize) {
        const batch = filesToProcess.slice(i, i + batchSize);
        const batchContent = await this.processBatch(batch, options.rootPath);
        allContent.push(...batchContent);
        totalChunks += batchContent.length;
        console.log(`Processed batch ${Math.floor(i/batchSize) + 1}/${Math.ceil(filesToProcess.length/batchSize)}`);
      }
      
      const combinedContent = allContent.join('\n\n---FILE_SEPARATOR---\n\n');
      console.log('Saving to RAG system...');
      const savePromise = this.storageTool.savePdfToByteStore(Buffer.from(combinedContent, 'utf-8'), `${options.projectName}_codebase`, combinedContent, 'Codebase Documentation', { projectName: options.projectName, indexedAt: new Date().toISOString(), totalFiles: files.length, rootPath: options.rootPath, type: 'codebase_index' });
      const timeoutPromise = new Promise((_, reject) => setTimeout(() => reject(new Error('Indexing timeout after 10 minutes')), 10 * 60 * 1000));
      const result = await Promise.race([savePromise, timeoutPromise]) as any;

      console.log(`✅ Successfully indexed codebase!`);
      return { success: true, totalFiles: files.length, totalChunks: result.totalChunks, documentId: result.documentId };
    } catch (error) {
      console.error('Error indexing codebase:', error);
      return { success: false, totalFiles: 0, totalChunks: 0, documentId: '', error: error instanceof Error ? error.message : 'Unknown error' };
    }
  }

  /**
   * Diagnostic method to analyze what files would be indexed without actually processing them.
   * Useful for troubleshooting indexing issues and understanding file discovery behavior.
   */
  async diagnoseIndexing(options: CodebaseIndexingOptions): Promise<{
    totalFilesFound: number;
    indexableFiles: string[];
    skippedDirectories: string[];
    errorPaths: string[];
    filesByExtension: Record<string, number>;
    summary: string;
  }> {
    const excludePatterns = options.excludePatterns || this.defaultExcludePatterns;
    const includeExtensions = options.includeExtensions || this.defaultIncludeExtensions;

    console.log(`🔍 Running indexing diagnostic for: ${options.rootPath}`);
    console.log(`📋 Exclude patterns: ${excludePatterns.join(', ')}`);
    console.log(`📋 Include extensions: ${includeExtensions.join(', ')}`);

    const files = await this.findCodeFiles(options.rootPath, excludePatterns, includeExtensions, true);
    // Diagnostic method scans entire rootPath, no selectedPaths needed

    // Analyze file extensions
    const filesByExtension: Record<string, number> = {};
    for (const file of files) {
      const ext = path.extname(file).toLowerCase();
      filesByExtension[ext] = (filesByExtension[ext] || 0) + 1;
    }

    const summary = `Found ${files.length} indexable files across ${Object.keys(filesByExtension).length} different extensions.`;

    return {
      totalFilesFound: files.length,
      indexableFiles: files.map(f => path.relative(options.rootPath, f)),
      skippedDirectories: [], // This would be populated by findCodeFiles in verbose mode
      errorPaths: [], // This would be populated by findCodeFiles in verbose mode
      filesByExtension,
      summary
    };
  }

  /**
   * Find all code files in a directory tree using a parallelized, optimized approach.
   * Enhanced with better path handling, improved exclusion logic, detailed logging,
   * and support for targeted path scanning when selectedPaths are provided.
   */
  private async findCodeFiles(
    rootPath: string,
    excludePatterns: string[],
    includeExtensions: string[],
    verbose: boolean = false,
    selectedPaths?: string[]
  ): Promise<string[]> {
    const includeExtSet = new Set(includeExtensions.map(ext => ext.toLowerCase()));
    const files: string[] = [];
    const skippedDirectories: string[] = [];
    const errorPaths: string[] = [];

    // Normalize the root path for better cross-platform compatibility
    const normalizedRootPath = path.resolve(rootPath);

    const traverse = async (currentPath: string, currentRelativePath: string): Promise<void> => {
      let items: Dirent[];
      try {
        items = await fs.readdir(currentPath, { withFileTypes: true });
      } catch (error) {
        const errorMsg = `Cannot read directory: ${currentPath} - ${error instanceof Error ? error.message : 'Unknown error'}`;
        errorPaths.push(errorMsg);
        if (verbose) console.warn(`⚠️  ${errorMsg}`);
        return;
      }

      const directoryPromises: Promise<void>[] = [];

      for (const item of items) {
        const itemName = item.name;
        const fullPath = path.join(currentPath, itemName);
        const relativePath = path.join(currentRelativePath, itemName);

        if (item.isDirectory()) {
          // Improved exclusion pattern matching with better path normalization
          const shouldExclude = excludePatterns.some(pattern => {
            // Normalize paths for consistent comparison across platforms
            const normalizedPattern = path.normalize(pattern).toLowerCase();
            const normalizedRelativePath = path.normalize(relativePath).toLowerCase();

            // Check exact match
            if (normalizedRelativePath === normalizedPattern) return true;

            // Check if directory starts with pattern (for nested exclusions)
            if (normalizedRelativePath.startsWith(normalizedPattern + path.sep)) return true;

            // Check if pattern appears as a complete directory segment
            const pathSegments = normalizedRelativePath.split(path.sep);
            const patternSegments = normalizedPattern.split(path.sep);

            // For single-segment patterns, check if any path segment matches
            if (patternSegments.length === 1) {
              return pathSegments.includes(normalizedPattern);
            }

            // For multi-segment patterns, check for substring match
            return normalizedRelativePath.includes(normalizedPattern);
          });

          if (shouldExclude) {
            skippedDirectories.push(relativePath);
            if (verbose) console.log(`⏭️  Skipping excluded directory: ${relativePath}`);
            continue;
          }

          directoryPromises.push(traverse(fullPath, relativePath));
        } else if (item.isFile()) {
          const ext = path.extname(itemName).toLowerCase();
          if (includeExtSet.has(ext)) {
            if (verbose) console.log(`📄 Found code file: ${relativePath}`);
            files.push(fullPath);
          } else if (verbose) {
            // Only log skipped files in verbose mode to avoid noise
            console.log(`⏭️  Skipping file with unsupported extension: ${relativePath} (${ext})`);
          }
        }
      }

      // Process directories in parallel for better performance
      await Promise.all(directoryPromises);
    };

    // Determine paths to scan: use selectedPaths if provided, otherwise scan the entire rootPath
    const pathsToScan = (selectedPaths && selectedPaths.length > 0) ? selectedPaths : [normalizedRootPath];

    if (verbose) {
      console.log(`🚀 Starting file discovery. Scanning ${pathsToScan.length} target location(s).`);
      if (selectedPaths && selectedPaths.length > 0) {
        console.log(`🎯 Targeted scanning of selected paths:`, selectedPaths);
      } else {
        console.log(`📂 Full directory scanning from: ${normalizedRootPath}`);
      }
    }

    // Process each target path
    const discoveryPromises = pathsToScan.map(async (startPath) => {
      const normalizedStartPath = path.resolve(startPath);
      try {
        const stats = await fs.stat(normalizedStartPath);
        if (stats.isDirectory()) {
          // It's a directory, traverse it
          const relativeToRoot = path.relative(normalizedRootPath, normalizedStartPath);
          await traverse(normalizedStartPath, relativeToRoot);
          if (verbose) console.log(`📁 Completed scanning directory: ${normalizedStartPath}`);
        } else if (stats.isFile()) {
          // It's a single file, check its extension and add if it matches
          const ext = path.extname(normalizedStartPath).toLowerCase();
          if (includeExtSet.has(ext)) {
            if (verbose) console.log(`📄 Found specified code file: ${normalizedStartPath}`);
            files.push(normalizedStartPath);
          } else if (verbose) {
            console.log(`⏭️  Skipping file with unsupported extension: ${normalizedStartPath} (${ext})`);
          }
        }
      } catch (error) {
        const errorMsg = `Cannot access target path: ${normalizedStartPath} - ${error instanceof Error ? error.message : 'Unknown error'}`;
        errorPaths.push(errorMsg);
        if (verbose) console.warn(`⚠️  ${errorMsg}`);
      }
    });

    await Promise.all(discoveryPromises);

    // Enhanced logging for debugging
    console.log(`✅ File discovery complete. Found ${files.length} code files from ${pathsToScan.length} target location(s).`);
    if (verbose) {
      console.log(`📊 Discovery Summary:`);
      console.log(`   - Total files found: ${files.length}`);
      console.log(`   - Directories skipped: ${skippedDirectories.length}`);
      console.log(`   - Errors encountered: ${errorPaths.length}`);

      if (skippedDirectories.length > 0 && skippedDirectories.length <= 20) {
        console.log(`📁 Skipped directories:`, skippedDirectories);
      } else if (skippedDirectories.length > 20) {
        console.log(`📁 Skipped directories (first 20):`, skippedDirectories.slice(0, 20));
        console.log(`   ... and ${skippedDirectories.length - 20} more`);
      }

      if (errorPaths.length > 0) {
        console.log(`❌ Error paths:`, errorPaths);
      }
    }

    return files;
  }
  
  private async processBatch(filePaths: string[], rootPath: string): Promise<string[]> {
    const contents: string[] = [];
    for (const filePath of filePaths) {
      try {
        const content = await fs.readFile(filePath, 'utf-8');
        const relativePath = path.relative(rootPath, filePath);
        const fileContent = `\n=== FILE: ${relativePath} ===\nLanguage: ${this.getLanguageFromExtension(path.extname(filePath))}\nPath: ${relativePath}\nSize: ${content.length} characters\n\n${content}\n\n=== END FILE: ${relativePath} ===\n`;
        contents.push(fileContent);
      } catch (error) {
        console.warn(`Could not read file: ${filePath}`);
      }
    }
    return contents;
  }

  private getLanguageFromExtension(ext: string): string {
    const languageMap: Record<string, string> = {
      '.ts': 'TypeScript', '.tsx': 'TypeScript React', '.js': 'JavaScript', '.jsx': 'JavaScript React',
      '.py': 'Python', '.java': 'Java', '.cpp': 'C++', '.c': 'C', '.cs': 'C#', '.go': 'Go', '.rs': 'Rust',
      '.php': 'PHP', '.rb': 'Ruby', '.swift': 'Swift', '.kt': 'Kotlin', '.scala': 'Scala', '.md': 'Markdown',
      '.txt': 'Text', '.json': 'JSON', '.yaml': 'YAML', '.yml': 'YAML'
    };
    return languageMap[ext] || 'Unknown';
  }

  /**
   * **PHASE 2: Enhanced Contextual File Analysis**
   */
  private async inferFilePurpose(filePath: string, language: string, fileContent: string): Promise<string> {
    const pathLower = filePath.toLowerCase();
    const fileName = path.basename(filePath).toLowerCase();
    const pathBasedPurpose = this.getPathBasedPurpose(pathLower, fileName, language);

    if (fileContent.trim().length > 100 && this.applicationContext) {
      try {
        return await this.analyzeFileWithContext(filePath, language, fileContent, pathBasedPurpose);
      } catch (error) {
        console.warn(`⚠️  Contextual file analysis failed for ${filePath}, using path-based purpose:`, error);
        return pathBasedPurpose;
      }
    }
    return pathBasedPurpose;
  }

  private getPathBasedPurpose(pathLower: string, fileName: string, language: string): string {
    if (pathLower.includes('/api/') || pathLower.includes('\\api\\')) return 'API route handler';
    if (pathLower.includes('/components/') || pathLower.includes('\\components\\')) return 'React component';
    if (pathLower.includes('/pages/') || pathLower.includes('\\pages\\') || pathLower.includes('/app/') || pathLower.includes('\\app\\')) return 'Application page/route';
    if (pathLower.includes('/utils/') || pathLower.includes('\\utils\\') || pathLower.includes('/lib/') || pathLower.includes('\\lib\\')) return 'Utility/library function';
    if (fileName.startsWith('use') && (language.includes('React') || language.includes('TypeScript'))) return 'React hook';
    if (fileName.includes('config') || fileName.includes('.config.') || fileName === 'package.json' || fileName.includes('tsconfig')) return 'Configuration file';
    if (fileName.includes('types') || fileName.includes('interface') || pathLower.includes('/types/') || pathLower.includes('\\types\\')) return 'Type definitions';
    if (fileName.includes('test') || fileName.includes('spec') || pathLower.includes('/tests/') || pathLower.includes('\\tests\\')) return 'Test file';
    if (fileName.includes('layout')) return 'Layout component';
    if (language.includes('React')) return 'React component or utility';
    if (language === 'TypeScript' || language === 'JavaScript') return 'JavaScript/TypeScript module';
    return `${language} source file`;
  }

  /**
   * Determines the optimal context limit based on file type and characteristics
   */
  private getAdaptiveContextLimit(filePath: string, language: string, fileContent: string): number {
    const fileName = path.basename(filePath).toLowerCase();
    const pathLower = filePath.toLowerCase();

    // Configuration files often have important settings at the end
    if (fileName.includes('config') || fileName.includes('.config.') ||
        fileName === 'package.json' || fileName === 'tsconfig.json' ||
        fileName.endsWith('.json') || fileName.endsWith('.yaml') || fileName.endsWith('.yml')) {
      return 2500;
    }

    // Type definition files need more context for interfaces and types
    if (fileName.includes('types') || fileName.includes('interface') ||
        pathLower.includes('/types/') || pathLower.includes('\\types\\') ||
        fileName.endsWith('.d.ts')) {
      return 2000;
    }

    // API routes and handlers often have important logic after imports
    if (pathLower.includes('/api/') || pathLower.includes('\\api\\') ||
        pathLower.includes('/routes/') || pathLower.includes('\\routes\\') ||
        fileName.includes('route') || fileName.includes('handler')) {
      return 1800;
    }

    // Main entry points and important architectural files
    if (fileName === 'index.ts' || fileName === 'index.js' || fileName === 'main.ts' ||
        fileName === 'app.ts' || fileName === 'app.js' || fileName.includes('entry')) {
      return 2000;
    }

    // Database models and schemas
    if (pathLower.includes('/models/') || pathLower.includes('\\models\\') ||
        pathLower.includes('/schema/') || pathLower.includes('\\schema\\') ||
        fileName.includes('model') || fileName.includes('schema')) {
      return 1800;
    }

    // React components with complex props
    if ((language.includes('React') || fileName.endsWith('.tsx') || fileName.endsWith('.jsx')) &&
        fileContent.includes('interface') && fileContent.includes('Props')) {
      return 1600;
    }

    // Test files might have complex setup
    if (fileName.includes('test') || fileName.includes('spec') ||
        pathLower.includes('/tests/') || pathLower.includes('\\tests\\')) {
      return 1500;
    }

    // Default for regular source files
    return 1200;
  }

  /**
   * Intelligently truncates content at natural breakpoints
   */
  private smartTruncateContent(content: string, maxChars: number): string {
    if (content.length <= maxChars) {
      return content;
    }

    // Try to break at the end of import/require section
    const importPatterns = [
      /\n(?=(?:export|class|function|const|let|var|interface|type)\s)/,
      /\n(?=\/\*\*[\s\S]*?\*\/\s*(?:export|class|function))/,
      /\n(?=\/\/[^\n]*\n(?:export|class|function))/
    ];

    for (const pattern of importPatterns) {
      const matches = Array.from(content.matchAll(new RegExp(pattern.source, 'g')));
      const lastMatch = matches[matches.length - 1];
      if (lastMatch && lastMatch.index && lastMatch.index < maxChars && lastMatch.index > maxChars * 0.6) {
        return content.substring(0, lastMatch.index);
      }
    }

    // Try to break at the end of a comment block
    const commentEnd = content.lastIndexOf('*/', maxChars);
    if (commentEnd > maxChars * 0.5 && commentEnd !== -1) {
      return content.substring(0, commentEnd + 2);
    }

    // Try to break at the end of a complete line
    const lastNewline = content.lastIndexOf('\n', maxChars);
    if (lastNewline > maxChars * 0.8) {
      return content.substring(0, lastNewline);
    }

    // Try to break at the end of a statement (semicolon or closing brace)
    const statementEnd = Math.max(
      content.lastIndexOf(';', maxChars),
      content.lastIndexOf('}', maxChars)
    );
    if (statementEnd > maxChars * 0.8) {
      return content.substring(0, statementEnd + 1);
    }

    // Fallback to character limit
    return content.substring(0, maxChars);
  }

  private async analyzeFileWithContext(filePath: string, language: string, fileContent: string, pathBasedPurpose: string): Promise<string> {
    const imports = this.extractImports(fileContent);
    const comments = this.extractComments(fileContent);

    // Use adaptive context limit and smart truncation
    const contextLimit = this.getAdaptiveContextLimit(filePath, language, fileContent);
    const truncatedContent = this.smartTruncateContent(fileContent, contextLimit);
    const truncationInfo = fileContent.length > contextLimit ?
      ` (showing ${truncatedContent.length}/${fileContent.length} characters)` : '';

    // Track cost savings data
    this.costTrackingData.push({
      originalSize: fileContent.length,
      truncatedSize: truncatedContent.length
    });

    const prompt = `
You are analyzing a specific file within a software application to determine its precise purpose and role.
**Application Context:**
${this.applicationContext}
**File Information:**
- Path: ${filePath}
- Language: ${language}
- Path-based Classification: ${pathBasedPurpose}
**File Analysis Data:**
- Import Dependencies: ${imports.length > 0 ? imports.join(', ') : 'None'}
- Comments/Documentation: ${comments.length > 0 ? comments.slice(0, 3).join(' | ') : 'None'}
**Task:**
Based on the application context and file analysis, provide a precise, contextual description of this file's purpose.
**Requirements:**
- Be specific about the file's role in THIS application.
- Keep the description concise but informative (1-2 sentences).
Respond with ONLY the purpose description as plain text.
**FILE CONTENT${truncationInfo}:**
\`\`\`${language}
${truncatedContent}${fileContent.length > contextLimit ? '\n...' : ''}
\`\`\`
    `;
    try {
      console.log(`🔍 Analyzing contextual purpose for: ${path.basename(filePath)} (${truncatedContent.length}/${fileContent.length} chars)`);
      const result = await processWithGoogleAI({ prompt, model: "gemini-2.5-flash" });
      const purpose = result.trim().replace(/^["']|["']$/g, '');
      return purpose || pathBasedPurpose;
    } catch (error) {
      console.warn(`⚠️  LLM analysis failed for ${filePath}:`, error);
      return pathBasedPurpose;
    }
  }

  /**
   * Calculates estimated cost savings from adaptive truncation
   */
  private calculateCostSavings(originalLength: number, truncatedLength: number): { savedTokens: number, savedCost: number } {
    const CHARS_PER_TOKEN = 4;
    const COST_PER_MILLION_TOKENS = 0.30; // Gemini 2.5 Flash input cost

    const originalTokens = Math.ceil(originalLength / CHARS_PER_TOKEN);
    const truncatedTokens = Math.ceil(truncatedLength / CHARS_PER_TOKEN);
    const savedTokens = originalTokens - truncatedTokens;
    const savedCost = (savedTokens / 1_000_000) * COST_PER_MILLION_TOKENS;

    return { savedTokens, savedCost };
  }

  /**
   * Logs cost optimization statistics for the indexing process
   */
  private logCostOptimization(files: { originalSize: number, truncatedSize: number }[]): void {
    const totalOriginal = files.reduce((sum, f) => sum + f.originalSize, 0);
    const totalTruncated = files.reduce((sum, f) => sum + f.truncatedSize, 0);
    const { savedTokens, savedCost } = this.calculateCostSavings(totalOriginal, totalTruncated);

    const reductionPercent = ((totalOriginal - totalTruncated) / totalOriginal * 100).toFixed(1);

    console.log(`\n💰 Adaptive Truncation Cost Optimization:`);
    console.log(`   📊 Content reduced by ${reductionPercent}% (${totalOriginal.toLocaleString()} → ${totalTruncated.toLocaleString()} chars)`);
    console.log(`   🪙 Tokens saved: ${savedTokens.toLocaleString()}`);
    console.log(`   💵 Estimated cost savings: $${savedCost.toFixed(4)}`);
    console.log(`   📈 Cost per file: ~$${(savedCost / files.length).toFixed(6)}`);
  }

  private extractComments(content: string): string[] {
    const comments: string[] = [];
    const singleLineRegex = /\/\/\s*(.+)/g;
    let match;
    while ((match = singleLineRegex.exec(content)) !== null) {
      const comment = match[1].trim();
      if (comment.length > 10) comments.push(comment);
    }
    const multiLineRegex = /\/\*\*?([\s\S]*?)\*\//g;
    while ((match = multiLineRegex.exec(content)) !== null) {
      const comment = match[1].replace(/\*/g, '').trim();
      if (comment.length > 10) comments.push(comment);
    }
    return comments.slice(0, 5);
  }

  private async processAndEnrichFile(filePath: string, options: CodebaseIndexingOptions, fileId: string): Promise<EnrichedCodeChunk[]> {
    let content: string;
    try {
      content = await fs.readFile(filePath, 'utf-8');
    } catch (error) {
      console.warn(`⚠️  Failed to read file: ${path.relative(options.rootPath, filePath)} - ${error instanceof Error ? error.message : 'Unknown error'}`);
      return [];
    }

    const relativePath = path.relative(options.rootPath, filePath);
    const fileName = path.basename(filePath);
    const language = this.getLanguageFromExtension(path.extname(filePath));

    const trimmedContent = content.trim();
    if (!trimmedContent || trimmedContent.length < 20) {
      if (options.verbose) {
        console.log(`⏭️  Skipping file with insufficient content: ${relativePath} (${trimmedContent.length} chars)`);
      }
      return [];
    }

    const imports = this.extractImports(content);
    const exports = this.extractExports(content);
    const apiEndpoints = this.extractApiEndpoints(content);

    const filePurpose = await this.inferFilePurpose(relativePath, language, content);
    const fileHeader = `=== FILE: ${relativePath} ===\nLanguage: ${language}\nPurpose: ${filePurpose}\nPath: ${relativePath}\n\n`;
    const fullContent = fileHeader + content;

    const textSplitter = new RecursiveCharacterTextSplitter({
      chunkSize: options.chunkSize || 2500,
      chunkOverlap: options.chunkOverlap || 300,
      separators: ["\n\n", "\n", ". ", " ", ""]
    });
    const rawChunks = await textSplitter.splitText(fullContent);
    const meaningfulChunks = rawChunks.filter(chunk => chunk.replace(/=== FILE:.*?\n[\s\S]*?\n\n/, '').trim().length > 50);

    const enrichedChunkPromises = meaningfulChunks.map(async (chunkContent: string, index: number) => {
      const { summary, entityType, definedEntities } = await this.analyzeChunkWithLLM(chunkContent, language);
      return {
        content: chunkContent,
        metadata: {
          title: fileName, filePath: relativePath, language: language,
          chunkIndex: index, projectName: options.projectName, type: 'code_chunk' as const,
          indexedAt: new Date().toISOString(), doc_id: '', chunk_id: '',
          codeSummary: summary, imports: imports, exports: exports,
          codeEntityType: entityType, definedEntities: definedEntities,
          apiEndpoints: apiEndpoints, fileId: fileId,
        }
      };
    });

    return Promise.all(enrichedChunkPromises);
  }

  private async analyzeChunkWithLLM(chunk: string, language: string): Promise<{ summary: string; entityType: 'Component' | 'Function' | 'Class' | 'Hook' | 'Configuration' | 'Util' | 'Unknown'; definedEntities: string[] }> {
    const filePathMatch = chunk.match(/=== FILE: (.*?) ===/);
    const purposeMatch = chunk.match(/Purpose: (.*?)\n/);
    const filePath = filePathMatch ? filePathMatch[1] : 'unknown';
    const filePurpose = purposeMatch ? purposeMatch[1] : 'unknown';

    const prompt = `
You are analyzing a code chunk from a software project. Provide a comprehensive analysis.
**Application Context:**
${this.applicationContext || 'Application context not available'}
**File Context:**
- File Path: ${filePath}
- Language: ${language}
- Contextual Purpose: ${filePurpose}
**Analysis Requirements:**
1. **Summary**: Provide a detailed summary (2-3 sentences) of the code's role, functionality, and architectural significance.
2. **Entity Type**: Classify the main entity type from: Component, Function, Class, Hook, Configuration, Util, Unknown.
3. **Defined Entities**: List ALL functions, components, classes, interfaces, types, or constants defined in this chunk.
Respond with ONLY a valid JSON object:
{
    "summary": "Detailed summary...",
    "entityType": "Component",
    "definedEntities": ["EntityName1", "functionName"]
}
**CODE CHUNK:**
\`\`\`${language}
${chunk}
\`\`\`
    `;

    try {
      console.log("CodebaseIndexingTool: Attempting primary analysis with Google Gemini 2.5 Flash");
      const result = await processWithGoogleAI({ prompt, model: "gemini-2.5-flash" });
      const jsonString = result.substring(result.indexOf('{'), result.lastIndexOf('}') + 1);
      const parsed = JSON.parse(jsonString);
      return this.validateAndSanitizeAnalysisResponse(parsed);
    } catch (googleError) {
      console.warn("CodebaseIndexingTool: Google Gemini 2.5 Flash analysis failed, attempting Groq fallback:", googleError);
      try {
        console.log("CodebaseIndexingTool: Attempting fallback analysis with Groq DeepSeek");
        const result = await processWithGroq({ prompt, model: 'deepseek-r1-distill-llama-70b', modelOptions: { temperature: 0.3, maxTokens: 2000 } });
        const jsonString = result.substring(result.indexOf('{'), result.lastIndexOf('}') + 1);
        const parsed = JSON.parse(jsonString);
        return this.validateAndSanitizeAnalysisResponse(parsed);
      } catch (groqError) {
        console.warn("CodebaseIndexingTool: Groq DeepSeek fallback also failed:", groqError);
        return this.getDefaultAnalysisResponse();
      }
    }
  }

  private validateAndSanitizeAnalysisResponse(parsed: any): { summary: string; entityType: 'Component' | 'Function' | 'Class' | 'Hook' | 'Configuration' | 'Util' | 'Unknown'; definedEntities: string[] } {
    const validEntityTypes = ['Component', 'Function', 'Class', 'Hook', 'Configuration', 'Util', 'Unknown'];
    return {
      summary: (typeof parsed?.summary === 'string' && parsed.summary.trim()) ? parsed.summary.trim() : "Code analysis summary not available.",
      entityType: (typeof parsed?.entityType === 'string' && validEntityTypes.includes(parsed.entityType)) ? parsed.entityType as any : 'Unknown',
      definedEntities: Array.isArray(parsed?.definedEntities) ? parsed.definedEntities.filter((e: any) => typeof e === 'string' && e.trim()).map((e: string) => e.trim()) : []
    };
  }
  
  private getDefaultAnalysisResponse(): { summary: string; entityType: 'Component' | 'Function' | 'Class' | 'Hook' | 'Configuration' | 'Util' | 'Unknown'; definedEntities: string[] } {
    return {
      summary: "No summary available due to LLM analysis failures.",
      entityType: "Unknown",
      definedEntities: []
    };
  }

  private sanitizeMetadataForPinecone(metadata: any): any {
    const sanitized: any = {};
    for (const [key, value] of Object.entries(metadata)) {
      if (value === null || value === undefined) {
        if (key === 'codeEntityType') sanitized[key] = 'Unknown';
        else if (Array.isArray(value) || key.includes('Entities') || key.includes('imports') || key.includes('exports') || key.includes('apiEndpoints')) sanitized[key] = [];
        else sanitized[key] = '';
      } else if (typeof value === 'string' || typeof value === 'number' || typeof value === 'boolean') {
        sanitized[key] = value;
      } else if (Array.isArray(value)) {
        sanitized[key] = value.filter(item => typeof item === 'string' && item.trim()).map(item => String(item).trim());
      } else if (typeof value === 'object') {
        sanitized[key] = JSON.stringify(value);
      } else {
        sanitized[key] = String(value);
      }
    }
    return sanitized;
  }

  private extractImports(content: string): string[] {
    const importRegex = /import(?:(?:(?:[ \n\t]+([^ \n\t\n]+)[ \n\t]*,?)?(?:[ \n\t]*\{(?:[ \n\t]*[^ \n\t\{\}]+[ \n\t]*,?)+\})?[ \n\t]*)from[ \n\t]*(?:['"])([^'"\n]+)(?:['"]))/g;
    return [...content.matchAll(importRegex)].map(match => match[2]);
  }

  private extractExports(content: string): string[] {
    const exportRegex = /export (?:const|let|var|function|class|default) (\w+)/g;
    return [...content.matchAll(exportRegex)].map(match => match[1]);
  }

  private extractApiEndpoints(content: string): string[] {
    const apiRegex = /(?:fetch|axios\.(?:get|post|put|delete|patch))\s*\(\s*['"`]([^'"`]+)['"`]/g;
    return [...content.matchAll(apiRegex)].map(match => match[1]);
  }
}

export const codebaseIndexingTool = new CodebaseIndexingTool();